"""
Mock fixtures for testing the Trigger Service.

This module provides mock objects and fixtures for external services,
including Google Calendar API, workflow service, and auth service mocks.
"""

from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest


@pytest.fixture
def mock_google_calendar_service():
    """Mock Google Calendar API service."""
    service = MagicMock()
    
    # Mock events() method
    events_mock = MagicMock()
    service.events.return_value = events_mock
    
    # Mock list() method
    list_mock = MagicMock()
    events_mock.list.return_value = list_mock
    list_mock.execute.return_value = {
        "items": [
            {
                "id": "test-event-123",
                "summary": "Test Event",
                "start": {"dateTime": "2024-01-15T10:00:00Z"},
                "end": {"dateTime": "2024-01-15T11:00:00Z"}
            }
        ]
    }
    
    # Mock get() method
    get_mock = MagicMock()
    events_mock.get.return_value = get_mock
    get_mock.execute.return_value = {
        "id": "test-event-123",
        "summary": "Test Event",
        "start": {"dateTime": "2024-01-15T10:00:00Z"},
        "end": {"dateTime": "2024-01-15T11:00:00Z"}
    }
    
    # Mock watch() method for webhooks
    watch_mock = MagicMock()
    events_mock.watch.return_value = watch_mock
    watch_mock.execute.return_value = {
        "kind": "api#channel",
        "id": "test-channel-123",
        "resourceId": "test-resource-456",
        "expiration": "1640995200000"
    }
    
    return service


@pytest.fixture
def mock_google_credentials():
    """Mock Google OAuth2 credentials."""
    credentials = MagicMock()
    credentials.token = "mock-access-token"
    credentials.refresh_token = "mock-refresh-token"
    credentials.expired = False
    credentials.valid = True
    
    # Mock refresh method
    credentials.refresh = MagicMock()
    
    return credentials


@pytest.fixture
def mock_workflow_executor():
    """Mock WorkflowExecutor for testing."""
    executor = MagicMock()
    executor.execute_workflow = AsyncMock(return_value="test-correlation-123")
    return executor


@pytest.fixture
def mock_auth_client():
    """Mock AuthClient for testing."""
    client = MagicMock()
    client.get_credentials = AsyncMock(return_value={
        "access_token": "mock-access-token",
        "refresh_token": "mock-refresh-token",
        "expires_at": "2024-12-31T23:59:59Z"
    })
    client.refresh_credentials = AsyncMock(return_value={
        "access_token": "new-mock-access-token",
        "refresh_token": "mock-refresh-token",
        "expires_at": "2024-12-31T23:59:59Z"
    })
    return client


@pytest.fixture
def mock_retry_handler():
    """Mock RetryHandler for testing."""
    handler = MagicMock()
    handler.execute_async = AsyncMock()
    handler.execute_sync = MagicMock()
    handler.is_retryable = MagicMock(return_value=True)
    return handler


@pytest.fixture
def mock_trigger_manager():
    """Mock TriggerManager for testing."""
    manager = MagicMock()
    manager.create_trigger = AsyncMock()
    manager.get_trigger = AsyncMock()
    manager.update_trigger = AsyncMock()
    manager.delete_trigger = AsyncMock()
    manager.list_triggers = AsyncMock()
    manager.process_event = AsyncMock()
    manager.get_statistics = AsyncMock()
    return manager


@pytest.fixture
def mock_dead_letter_queue():
    """Mock DeadLetterQueue for testing."""
    dlq = MagicMock()
    dlq.add_failed_execution = AsyncMock(return_value=True)
    dlq.get_failed_executions = AsyncMock(return_value=[])
    dlq.retry_execution = AsyncMock(return_value=(True, "test-correlation-123"))
    dlq.retry_batch = AsyncMock(return_value={
        "total": 5,
        "successful": 3,
        "failed": 2,
        "details": []
    })
    dlq.get_failure_statistics = AsyncMock(return_value={
        "total_failed_executions": 10,
        "recent_failures_24h": 2
    })
    dlq.cleanup_old_failures = AsyncMock(return_value={
        "deleted_count": 5,
        "dry_run": False
    })
    dlq.get_dlq_health = AsyncMock(return_value={
        "status": "healthy",
        "current_failed_executions": 0
    })
    return dlq


@pytest.fixture
def mock_google_calendar_adapter():
    """Mock GoogleCalendarAdapter for testing."""
    adapter = MagicMock()
    adapter.setup_trigger = AsyncMock(return_value=True)
    adapter.remove_trigger = AsyncMock(return_value=True)
    adapter.process_event = AsyncMock(return_value=True)
    adapter.get_health = AsyncMock(return_value={
        "status": "healthy",
        "external_service_status": "available"
    })
    adapter.pause_trigger = AsyncMock(return_value=True)
    adapter.resume_trigger = AsyncMock(return_value=True)
    adapter.get_trigger_info = AsyncMock(return_value={
        "subscription_id": "test-subscription-123",
        "webhook_url": "https://example.com/webhook"
    })
    return adapter


class MockLogger:
    """Mock logger for testing."""
    
    def __init__(self):
        self.debug = MagicMock()
        self.info = MagicMock()
        self.warning = MagicMock()
        self.error = MagicMock()
        self.critical = MagicMock()


@pytest.fixture
def mock_logger():
    """Mock logger fixture."""
    return MockLogger()


@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    settings = MagicMock()
    settings.database_url = "sqlite+aiosqlite:///:memory:"
    settings.workflow_service_url = "http://localhost:8001"
    settings.workflow_service_api_key = "test-workflow-key"
    settings.auth_service_url = "http://localhost:8002"
    settings.auth_service_api_key = "test-auth-key"
    settings.google_calendar_webhook_url = "http://localhost:8000/api/v1/webhooks/google-calendar"
    settings.max_retry_attempts = 5
    settings.retry_backoff_factor = 2.0
    settings.retry_max_delay = 300
    settings.http_timeout = 30
    return settings


# Context managers for patching
@pytest.fixture
def patch_get_settings(mock_settings):
    """Patch get_settings function."""
    with patch("src.utils.config.get_settings", return_value=mock_settings):
        yield mock_settings


@pytest.fixture
def patch_get_logger(mock_logger):
    """Patch get_logger function."""
    with patch("src.utils.logger.get_logger", return_value=mock_logger):
        yield mock_logger


@pytest.fixture
def patch_google_calendar_service(mock_google_calendar_service):
    """Patch Google Calendar service creation."""
    with patch("googleapiclient.discovery.build", return_value=mock_google_calendar_service):
        yield mock_google_calendar_service


@pytest.fixture
def patch_google_credentials(mock_google_credentials):
    """Patch Google credentials."""
    with patch("google.oauth2.credentials.Credentials", return_value=mock_google_credentials):
        yield mock_google_credentials


# Exception fixtures for testing error scenarios
@pytest.fixture
def google_api_error():
    """Mock Google API error."""
    from googleapiclient.errors import HttpError
    
    # Create a mock response
    mock_response = MagicMock()
    mock_response.status = 403
    mock_response.reason = "Forbidden"
    
    # Create the error
    error = HttpError(mock_response, b'{"error": {"message": "Forbidden"}}')
    return error


@pytest.fixture
def oauth_error():
    """Mock OAuth error."""
    from google.auth.exceptions import RefreshError
    return RefreshError("Token refresh failed")


@pytest.fixture
def database_error():
    """Mock database error."""
    from sqlalchemy.exc import SQLAlchemyError
    return SQLAlchemyError("Database connection failed")


@pytest.fixture
def validation_error():
    """Mock validation error."""
    from pydantic import ValidationError
    return ValidationError.from_exception_data(
        "ValidationError",
        [{"type": "missing", "loc": ("field",), "msg": "Field required"}]
    )


# Async context manager mocks
class MockAsyncContextManager:
    """Mock async context manager."""
    
    def __init__(self, return_value=None):
        self.return_value = return_value
    
    async def __aenter__(self):
        return self.return_value
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


@pytest.fixture
def mock_async_context_manager():
    """Create a mock async context manager."""
    return MockAsyncContextManager


# UUID fixtures
@pytest.fixture
def sample_uuid():
    """Sample UUID for testing."""
    return uuid4()


@pytest.fixture
def sample_uuids():
    """Multiple sample UUIDs for testing."""
    return [uuid4() for _ in range(5)]
