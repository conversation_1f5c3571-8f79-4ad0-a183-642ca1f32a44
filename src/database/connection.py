"""
Database connection management for the Trigger Service.

This module provides database connection setup, session management,
and connection pooling for PostgreSQL using SQLAlchemy.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from sqlalchemy.pool import QueuePool

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class Base(DeclarativeBase):
    """Base class for all database models."""

    pass


class DatabaseManager:
    """
    Database connection and session manager.

    This class handles database connection setup, session management,
    and provides both sync and async database operations.
    """

    def __init__(self):
        """Initialize the database manager."""
        self.settings = get_settings()
        self._engine = None
        self._async_engine = None
        self._session_factory = None
        self._async_session_factory = None

    def get_engine(self):
        """
        Get or create the synchronous database engine.

        Returns:
            Engine: SQLAlchemy engine instance
        """
        if self._engine is None:
            # Convert async URL to sync URL for synchronous operations
            sync_url = self.settings.database_url.replace(
                "postgresql+asyncpg://", "postgresql+psycopg2://"
            )
            if not sync_url.startswith("postgresql+psycopg2://"):
                sync_url = sync_url.replace("postgresql://", "postgresql+psycopg2://")

            self._engine = create_engine(
                sync_url,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=self.settings.debug,
            )

            # Add connection event listeners
            @event.listens_for(self._engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                """Set database connection parameters."""
                if hasattr(dbapi_connection, "set_isolation_level"):
                    # For PostgreSQL, set isolation level
                    dbapi_connection.set_isolation_level(1)  # READ_COMMITTED

            logger.info("Synchronous database engine created")

        return self._engine

    def get_async_engine(self):
        """
        Get or create the asynchronous database engine.

        Returns:
            AsyncEngine: SQLAlchemy async engine instance
        """
        if self._async_engine is None:
            # Ensure URL is async-compatible
            async_url = self.settings.database_url
            if not async_url.startswith("postgresql+asyncpg://"):
                async_url = async_url.replace("postgresql://", "postgresql+asyncpg://")
                async_url = async_url.replace(
                    "postgresql+psycopg2://", "postgresql+asyncpg://"
                )

            self._async_engine = create_async_engine(
                async_url,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=self.settings.debug,
            )

            logger.info("Asynchronous database engine created")

        return self._async_engine

    def get_session_factory(self):
        """
        Get or create the synchronous session factory.

        Returns:
            sessionmaker: SQLAlchemy session factory
        """
        if self._session_factory is None:
            self._session_factory = sessionmaker(
                bind=self.get_engine(),
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
            )
            logger.info("Synchronous session factory created")

        return self._session_factory

    def get_async_session_factory(self):
        """
        Get or create the asynchronous session factory.

        Returns:
            async_sessionmaker: SQLAlchemy async session factory
        """
        if self._async_session_factory is None:
            self._async_session_factory = async_sessionmaker(
                bind=self.get_async_engine(),
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
            )
            logger.info("Asynchronous session factory created")

        return self._async_session_factory

    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get an async database session with automatic cleanup.

        Yields:
            AsyncSession: Database session
        """
        session_factory = self.get_async_session_factory()
        async with session_factory() as session:
            try:
                logger.debug("Database session created")
                yield session
                await session.commit()
                logger.debug("Database session committed")
            except Exception as e:
                await session.rollback()
                logger.error("Database session rolled back", error=str(e))
                raise
            finally:
                await session.close()
                logger.debug("Database session closed")

    def get_session(self):
        """
        Get a synchronous database session.

        Returns:
            Session: Database session
        """
        session_factory = self.get_session_factory()
        return session_factory()

    async def health_check(self) -> bool:
        """
        Check database connectivity.

        Returns:
            bool: True if database is accessible, False otherwise
        """
        try:
            async with self.get_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False

    async def close(self) -> None:
        """Close all database connections."""
        if self._async_engine:
            await self._async_engine.dispose()
            logger.info("Async database engine disposed")

        if self._engine:
            self._engine.dispose()
            logger.info("Sync database engine disposed")


# Global database manager instance
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """
    Get the global database manager instance.

    Returns:
        DatabaseManager: Database manager instance
    """
    return db_manager


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function to get an async database session.

    This function is designed to be used with FastAPI's dependency injection.

    Yields:
        AsyncSession: Database session
    """
    async with db_manager.get_async_session() as session:
        yield session


def get_session():
    """
    Get a synchronous database session.

    Returns:
        Session: Database session
    """
    return db_manager.get_session()


async def init_database() -> None:
    """Initialize database connections and verify connectivity."""
    logger.info("Initializing database connections")

    # Test database connectivity
    if await db_manager.health_check():
        logger.info("Database connection successful")
    else:
        logger.error("Database connection failed")
        raise RuntimeError("Failed to connect to database")


async def close_database() -> None:
    """Close database connections."""
    logger.info("Closing database connections")
    await db_manager.close()
