"""
Health check API routes.

This module contains health check endpoints for monitoring service status
and dependencies.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any, List
from datetime import datetime

from src.core.trigger_manager import TriggerManager
from src.database.connection import db_manager
from src.schemas.trigger import AdapterHealthResponse
from src.api.middleware.auth import require_auth, get_current_user
from src.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/health", tags=["health"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

        logger.info("TriggerManager singleton created with registered adapters")
    return get_trigger_manager._instance


@router.get("/")
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.

    This endpoint provides a simple health status without authentication
    and is suitable for load balancer health checks.

    Returns:
        Dict[str, Any]: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "trigger-service",
        "version": "1.0.0",
    }


@router.get("/detailed")
async def detailed_health_check(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> Dict[str, Any]:
    """
    Detailed health check with dependency status.

    This endpoint requires authentication and provides detailed health
    information including database and adapter status.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        Dict[str, Any]: Detailed health status

    Raises:
        HTTPException: If authentication fails
    """
    require_auth(request)
    user_id = get_current_user(request)

    logger.info(f"Detailed health check requested by user {user_id}")

    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "trigger-service",
        "version": "1.0.0",
        "user_id": user_id,
        "dependencies": {},
    }

    # Check database health
    try:
        db_healthy = await db_manager.health_check()
        health_status["dependencies"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "checked_at": datetime.now().isoformat(),
        }
    except Exception as e:
        health_status["dependencies"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "checked_at": datetime.now().isoformat(),
        }
        health_status["status"] = "degraded"

    # Check adapter health
    try:
        adapter_health = await trigger_manager.health_check()
        health_status["dependencies"]["adapters"] = {}

        all_adapters_healthy = True
        for adapter_name, adapter_status in adapter_health.items():
            health_status["dependencies"]["adapters"][adapter_name] = {
                "status": "healthy" if adapter_status.is_healthy else "unhealthy",
                "last_check": adapter_status.last_check.isoformat(),
                "error_message": adapter_status.error_message,
            }
            if not adapter_status.is_healthy:
                all_adapters_healthy = False

        if not all_adapters_healthy and health_status["status"] == "healthy":
            health_status["status"] = "degraded"

    except Exception as e:
        health_status["dependencies"]["adapters"] = {
            "status": "unhealthy",
            "error": str(e),
            "checked_at": datetime.now().isoformat(),
        }
        health_status["status"] = "degraded"

    # Add trigger statistics
    try:
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        health_status["statistics"] = {
            "total_triggers": len(triggers),
            "active_triggers": len([t for t in triggers if t.is_active]),
            "registered_adapters": len(trigger_manager.list_adapters()),
        }
    except Exception as e:
        logger.warning(
            f"Failed to get trigger statistics for health check", error=str(e)
        )
        health_status["statistics"] = {"error": "Failed to retrieve statistics"}

    return health_status


@router.get("/adapters", response_model=List[AdapterHealthResponse])
async def adapter_health_check(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> List[AdapterHealthResponse]:
    """
    Get health status for all registered adapters.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        List[AdapterHealthResponse]: List of adapter health statuses

    Raises:
        HTTPException: If authentication fails or health check fails
    """
    require_auth(request)
    user_id = get_current_user(request)

    logger.info(f"Adapter health check requested by user {user_id}")

    try:
        adapter_health = await trigger_manager.health_check()

        health_responses = []
        for adapter_name, health_status in adapter_health.items():
            # Get adapter statistics
            adapter = trigger_manager.get_adapter(adapter_name)
            active_triggers = adapter.get_trigger_count() if adapter else 0

            health_response = AdapterHealthResponse(
                adapter_name=adapter_name,
                is_healthy=health_status.is_healthy,
                last_check=health_status.last_check,
                error_message=health_status.error_message,
                active_triggers=active_triggers,
                external_service_status=health_status.external_service_status,
            )
            health_responses.append(health_response)

        return health_responses

    except Exception as e:
        logger.error(f"Failed to get adapter health status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve adapter health status",
        )


@router.get("/metrics")
async def get_metrics(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> Dict[str, Any]:
    """
    Get service metrics for monitoring.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        Dict[str, Any]: Service metrics

    Raises:
        HTTPException: If authentication fails
    """
    require_auth(request)
    user_id = get_current_user(request)

    logger.info(f"Metrics requested by user {user_id}")

    try:
        # Get adapter statistics
        adapter_stats = await trigger_manager.get_adapter_statistics()

        # Get user's triggers for statistics
        triggers = await trigger_manager.get_triggers_for_user(user_id)

        metrics = {
            "timestamp": datetime.now().isoformat(),
            "service": "trigger-service",
            "user_metrics": {
                "user_id": user_id,
                "total_triggers": len(triggers),
                "active_triggers": len([t for t in triggers if t.is_active]),
                "inactive_triggers": len([t for t in triggers if not t.is_active]),
                "triggers_by_type": {},
            },
            "adapter_metrics": adapter_stats,
            "system_metrics": {
                "registered_adapters": len(trigger_manager.list_adapters()),
                "uptime_seconds": 0,  # Placeholder - would track actual uptime
                "memory_usage_mb": 0,  # Placeholder - would track actual memory
                "cpu_usage_percent": 0,  # Placeholder - would track actual CPU
            },
        }

        # Calculate triggers by type for user
        for trigger in triggers:
            trigger_type = trigger.trigger_type
            if trigger_type not in metrics["user_metrics"]["triggers_by_type"]:
                metrics["user_metrics"]["triggers_by_type"][trigger_type] = 0
            metrics["user_metrics"]["triggers_by_type"][trigger_type] += 1

        return metrics

    except Exception as e:
        logger.error(f"Failed to get metrics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics",
        )
